import os
from langchain_ollama.llms import OllamaLLM
from langchain_.prompts import ChatPromptTemplate

# Override the base URL to use localhost since we're connecting to Docker from host
llm_base_url = "http://localhost:11434"  # Changed from Docker internal URL
model_name = os.getenv("LLM_CHOICE", "llama3.2:latest")  # Use available model

llm = OllamaLLM(base_url=llm_base_url, model=model_name)

template = """
You are an expert in answereing questions about a pizza restaurant.

Here are some relevant reviews: {reviews}

Here is the question to answer: {question}
"""

prompt = ChatPromptTemplate.from_template(template)
chain = prompt | llm

result = chain.invoke({"reviews": [], "question": "What is the best pizza place?"})

print(result)